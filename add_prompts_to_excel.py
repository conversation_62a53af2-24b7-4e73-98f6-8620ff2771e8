#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将生成的Prompt内容添加到Excel文件的新列中
"""

import pandas as pd
import os
import glob
from openpyxl import load_workbook

def load_generated_prompts(prompts_dir):
    """加载所有生成的Prompt文件内容"""
    prompts_dict = {}
    
    # 获取所有prompt文件
    prompt_files = glob.glob(os.path.join(prompts_dir, "prompt_*.txt"))
    print(f"找到 {len(prompt_files)} 个Prompt文件")
    
    for file_path in prompt_files:
        # 从文件名提取sessionId
        filename = os.path.basename(file_path)
        session_id = filename.replace('prompt_', '').replace('.txt', '')
        
        try:
            # 读取Prompt内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            prompts_dict[int(session_id)] = content
            
        except Exception as e:
            print(f"读取文件 {filename} 失败: {e}")
    
    print(f"成功加载 {len(prompts_dict)} 个Prompt内容")
    return prompts_dict

def add_prompts_to_excel(excel_path, prompts_dict, output_path):
    """将Prompt内容添加到Excel文件"""
    print(f"正在处理Excel文件: {excel_path}")
    
    # 读取Excel文件
    df = pd.read_excel(excel_path, sheet_name='待解析')
    print(f"Excel数据行数: {len(df)}")
    
    # 创建新列
    df['最终Prompt'] = ""
    
    # 填充Prompt内容
    matched_count = 0
    for index, row in df.iterrows():
        session_id = int(row['sessionId'])
        
        if session_id in prompts_dict:
            df.loc[index, '最终Prompt'] = prompts_dict[session_id]
            matched_count += 1
        else:
            df.loc[index, '最终Prompt'] = "未找到对应的Prompt文件"
    
    print(f"成功匹配 {matched_count}/{len(df)} 行数据")
    
    # 保存到新文件
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='待解析', index=False)
    
    print(f"已保存到: {output_path}")
    
    # 调整列宽以便查看（可选）
    try:
        wb = load_workbook(output_path)
        ws = wb['待解析']
        
        # 设置最终Prompt列的宽度
        prompt_col_letter = chr(ord('A') + len(df.columns) - 1)  # 最后一列
        ws.column_dimensions[prompt_col_letter].width = 50
        
        wb.save(output_path)
        print("已调整列宽")
    except Exception as e:
        print(f"调整列宽失败: {e}")
    
    return matched_count

def main():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 文件路径
    excel_path = os.path.join(current_dir, "待解析1.xlsx")
    prompts_dir = os.path.join(current_dir, "generated_prompts")
    output_path = os.path.join(current_dir, "待解析1_含最终Prompt.xlsx")
    
    print("=== 将Prompt添加到Excel文件 ===")
    print(f"Excel文件: {excel_path}")
    print(f"Prompt目录: {prompts_dir}")
    print(f"输出文件: {output_path}")
    print("-" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        return
        
    if not os.path.exists(prompts_dir):
        print(f"❌ Prompt目录不存在: {prompts_dir}")
        return
    
    try:
        # 加载Prompt内容
        prompts_dict = load_generated_prompts(prompts_dir)
        
        if not prompts_dict:
            print("❌ 没有找到任何Prompt文件")
            return
        
        # 添加到Excel
        matched_count = add_prompts_to_excel(excel_path, prompts_dict, output_path)
        
        print(f"\n🎉 完成！")
        print(f"📊 匹配成功: {matched_count} 行")
        print(f"📁 输出文件: {output_path}")
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()