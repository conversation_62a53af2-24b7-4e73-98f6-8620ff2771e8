#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
import sys

def analyze_excel_file(file_path):
    """分析Excel文件的数据结构"""
    print(f"分析文件：{file_path}")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print("❌ 文件不存在")
        return
    
    print("✓ 文件存在")
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"\n1. 工作表信息：")
        print(f"   工作表数量：{len(sheet_names)}")
        print(f"   工作表名称：{sheet_names}")
        
        # 分析每个工作表的数据结构
        for i, sheet_name in enumerate(sheet_names, 1):
            print(f"\n{'='*60}")
            print(f"工作表 {i}：'{sheet_name}'")
            print(f"{'='*60}")
            
            try:
                # 读取工作表数据
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                print(f"2. 基本信息：")
                print(f"   数据行数：{len(df)} 行")
                print(f"   列数：{len(df.columns)} 列")
                print(f"   数据形状：{df.shape}")
                
                print(f"\n3. 列名列表：")
                for idx, col in enumerate(df.columns, 1):
                    print(f"   {idx:2d}. {col}")
                
                print(f"\n4. 关键列识别：")
                # 定义关键词列表
                key_words = [
                    '信号', '外呼记录', '对话历史', '业务知识列表',
                    '通话记录', '客户信息', '问题分类', '处理结果',
                    '呼叫', '录音', '对话', '客服', '问答', '知识库'
                ]
                
                found_key_columns = []
                for key_word in key_words:
                    matching_cols = [col for col in df.columns if key_word in str(col)]
                    if matching_cols:
                        for col in matching_cols:
                            if col not in found_key_columns:
                                found_key_columns.append(col)
                                print(f"   ✓ 关键列：'{col}' (包含关键词: {key_word})")
                
                if not found_key_columns:
                    print("   ⚠ 未找到预期的关键列")
                
                print(f"\n5. 数据类型分析：")
                for col in df.columns:
                    dtype = df[col].dtype
                    unique_count = df[col].nunique()
                    print(f"   {col}: {dtype} (唯一值: {unique_count})")
                
                print(f"\n6. 数据完整性检查：")
                print(f"   空值统计：")
                null_counts = df.isnull().sum()
                total_rows = len(df)
                
                for col in df.columns:
                    null_count = null_counts[col]
                    null_percentage = (null_count / total_rows) * 100 if total_rows > 0 else 0
                    status = "✓" if null_count == 0 else "⚠" if null_percentage < 50 else "❌"
                    print(f"     {status} {col}: {null_count} 个空值 ({null_percentage:.1f}%)")
                
                print(f"\n7. 数据预览（前3行）：")
                if len(df) > 0:
                    # 限制显示宽度避免过长输出
                    pd.set_option('display.max_columns', None)
                    pd.set_option('display.width', None)
                    pd.set_option('display.max_colwidth', 50)
                    print(df.head(3).to_string())
                else:
                    print("   数据表为空")
                
                print(f"\n8. 数据样本分析：")
                for col in df.columns[:5]:  # 只分析前5列避免输出过长
                    if len(df) > 0:
                        sample_values = df[col].dropna().head(3).tolist()
                        print(f"   {col} 样本值: {sample_values}")
                
            except Exception as e:
                print(f"   ❌ 读取工作表 '{sheet_name}' 时出错：{str(e)}")
                
    except Exception as e:
        print(f"❌ 读取Excel文件时出错：{str(e)}")

if __name__ == "__main__":
    file_path = "/Users/<USER>/Cursor-Project/Temp-excel/待解析1.xlsx"
    analyze_excel_file(file_path)